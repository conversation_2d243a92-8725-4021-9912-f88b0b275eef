<template>
  <el-card class="card_style" shadow="hover">
    <!-- 头部区域 -->
    <template #header>
      <div class="header-container" style="display: flex; align-items: center;">
        <el-page-header @back="goBack" />
        <!-- 地图工具栏 -->
        <div class="map-toolbar" v-show="!state.empty && !state.imgLoading">
          <el-button-group style="margin-right: 10px;">
            <el-button @click="resetMapView" title="恢复地图原始视图">
              <el-icon><Refresh /></el-icon>
              重置视图
            </el-button>
          </el-button-group>
          <el-button @click="toggleRulerMode" :type="rulerConfig.isRulerMode ? 'success' : 'primary'" title="空格键快捷操作">
            <el-icon><Tools /></el-icon>
            {{ rulerConfig.isRulerMode ? '退出测量' : '测量工具' }}
          </el-button>
 
        </div>

      </div>
    </template>
    <!-- 主体内容区 -->
    <template #default>
      <div class="content-container">

        <!-- 画布区域 -->
        <div
ref="canvasParent" v-loading="state.imgLoading" element-loading-text="地图加载中..."
          style="width: 100%; height: 100%">
          <el-empty description="请选择楼层地图" v-if="state.empty" style="width: 100%; height: 100%" />
          <canvas :id="state.canvasId" style="width: 100%; height: 100%"></canvas>
          <!-- 移除原有的尺寸信息展示 -->
              <!-- 状态指示器和操作提示 -->

          <div class="operation-tips" style="position: absolute; bottom: 7vh; z-index: 1000;">
            <div class="tip-line">
              <el-icon><Ruler /></el-icon> 测量工具提示
            </div>
            <div class="tip-line">
              <span class="tip-label">调整：</span>拖拽十字架端点改变尺寸和位置
            </div>
            <div class="tip-line">
              <span class="tip-label">编辑：</span>双击文本可修改实际距离
            </div>
            <div class="tip-line">
              <span class="tip-label">删除：</span>悬浮在十字架上按Delete键删除
            </div>
        </div>
    <div v-if="!state.empty && !state.imgLoading" class="status-indicator">
        <!-- 操作提示 -->
        <div class="operation-tips">
          <!-- 普通模式提示 -->
          <div v-if="!rulerConfig.isRulerMode" class="tip-line">
            空格: 进入测量模式 | 鼠标滚轮: 缩放地图
          </div>
          
          <!-- 尺子模式 - 设置起点阶段 -->
          <div v-else-if="rulerConfig.rulerStep === 0" class="tip-line">
            第一次点击: 设置起点 | 空格/Esc: 退出测量
          </div>
          
          <!-- 尺子模式 - 设置终点阶段 -->
          <div v-else-if="rulerConfig.rulerStep === 1" class="tip-line">
            第二次点击: 设置终点并完成测量 | Esc: 取消当前绘制
          </div>
          
          <!-- 尺子模式 - 已有尺子时的提示 -->
          <template v-else-if="rulerConfig.isRulerMode && hasRulers">
            <div class="tip-line">
              拖拽十字架: 调整大小/位置 | 双击文本: 编辑距离 | 空格/Esc: 退出测量
            </div>
            <div class="tip-line delete-tip">
              悬浮在十字架上按Delete: 删除尺子
            </div>
          </template>
          
          <!-- 尺子模式 - 无尺子时的提示 -->
          <div v-else-if="rulerConfig.isRulerMode" class="tip-line">
            点击开始测量 | 空格/Esc: 退出测量模式
          </div>
        </div>
      </div>
        </div>
        <!-- 侧边工具栏 - 围栏设置表单 -->
        <SideToolbar :title="state.dialogTitle" style="top: 105px; right: 50px">
          <el-form ref="formRef" :model="state.formData" :rules="formRules" label-suffix=":" label-width="100px">
            <!-- 地图选择 -->
            <el-form-item label="地图名称" prop="floorId">
              <el-tree-select
v-model="state.formData.floorId" :data="state.floorTree" check-strictly
                :props="{ ...defaultProps, label: 'floorName' }" placeholder="请选择地图名称" @change="handleFloorChange"
                clearable />
            </el-form-item>
            <el-form-item label="围栏名称" prop="fenceName">
              <el-input size="small" placeholder="请输入" v-model="state.formData.fenceName" />
            </el-form-item>
            <el-form-item label="围栏等级" prop="level">
              <el-select size="small" placeholder="请选择" v-model="state.formData.level">
                <el-option
v-for="dict in getStrDictOptions(DICT_TYPE.FENCE_LEVEL)" :key="dict.value"
                  :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="日期有效期" prop="expiryDate">
              <el-date-picker
size="small" v-model="state.expiryDate" type="daterange" unlink-panels
                value-format="YYYY-MM-DD" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                :shortcuts="shortcuts" @change="handleExpiryDateChange" />
            </el-form-item>
            <el-form-item label="时间有效期" prop="expiryTime">
              <el-time-picker
size="small" v-model="state.expiryTime" is-range value-format="HH:mm:ss"
                range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" @change="handleExpiryTimeChange" />
            </el-form-item>
            <el-form-item label="围栏区域" prop="areaIds">
              <el-tree-select
v-model="state.formData.areaIds" @remove-tag="handleAreaIdsChange" :data="state.areaTree"
                :props="{ children: 'children', label: 'areaName', value: 'id' }" multiple :render-after-expand="false"
                show-checkbox check-strictly node-key="id" check-on-click-node style="width: 240px"
                @check="handleAreaIdsChange" />
            </el-form-item>
            <!-- 围栏绘制工具 -->
            <el-form-item label="围栏绘制">
              <div style="display: flex; gap: 8px; margin-bottom: 10px">
                <el-button type="primary" circle @click="handleDraw('circle')" title="绘制圆形围栏">
                  <template #icon>
                    <Icon icon="svg-icon:tx-circle" :size="20" />
                  </template>
                </el-button>
                <el-button type="primary" circle @click="handleDraw('rectangle')" title="绘制矩形围栏">
                  <template #icon>
                    <Icon icon="svg-icon:tx-rectangle" :size="20" />
                  </template>
                </el-button>
                <el-button type="primary" circle @click="handleDraw('polygon')" title="绘制多边形围栏">
                  <template #icon>
                    <Icon icon="svg-icon:tx-polygon" :size="20" />
                  </template>
                </el-button>
                <el-button type="danger" circle @click="handleDraw('clear')" title="清除围栏">
                  <template #icon>
                    <Icon icon="svg-icon:tx-clear" :size="20" />
                  </template>
                </el-button>
              </div>
            </el-form-item>
            <!-- 使用说明 -->
            <el-alert type="info" :closable="false" show-icon style="margin-top: 10px">
              <template #title>
                <span style="font-size: 13px; font-weight: 500">围栏绘制说明</span>
              </template>
              <div style=" margin-top: 5px;font-size: 12px; line-height: 1.4">
                <p style="margin: 2px 0"><strong>圆形：</strong>点击起点，拖拽到终点确定半径</p>
                <p style="margin: 2px 0"><strong>矩形：</strong>点击起点，拖拽到对角确定大小</p>
                <p style="margin: 2px 0"><strong>多边形：</strong>依次点击各个顶点，双击结束绘制</p>
                <p style="margin: 2px 0; color: #409EFF"><strong>提示：</strong>双击尺寸数字可直接编辑修改</p>
              </div>
            </el-alert>
          </el-form>
        </SideToolbar>
      </div>
    </template>
    <!-- 底部操作区 -->
    <template #footer>
      <el-form>
        <el-form-item style="float: right">
          <el-button :loading="state.formLoading" type="primary" @click="submitForm">保存</el-button>
          <el-button @click="goBack">返回</el-button>
        </el-form-item>
      </el-form>
    </template>
  </el-card>

</template>

<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Tools } from '@element-plus/icons-vue'
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import { defaultProps, findNode } from '@/utils/tree'
import SideToolbar from '@/components/SideToolbar/SideToolbar.vue'
import { floorApi, floorVO } from '@/api/master/floor'
import { handleTree } from '@/utils/tree'
import { ref, reactive, computed, onMounted, nextTick, onUnmounted } from 'vue'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import { Iposition } from '@/api/master/floor/type'
import FabricCanvas from '@/utils/FabricCanvas'
import {  shortcuts } from '@/store/modules/canvas/drawing'
import { FenceApi, FenceVO, FloorTree } from '@/api/master/fence'
import { AreaApi, AreaNode } from '@/api/master/area'
import { rulerConfig, RulerTool } from '@/views/master/fence/components/rulerConfig'



// 导入围栏工具函数
import { FenceFactory } from '@/utils/fence/fence-factory'
import { FenceDate } from '@/utils/fence/type'
import { checkScaleAndOrigin, cleanupFenceElements, getScreenInfo } from '@/utils/fence/fence-utis'
import { addFence } from '../../../../utils/fence'
// Polygon类现在由FenceFactory管理，不需要直接导入

const route = useRoute() // 路由参数
const router = useRouter() // 路由实例
const { t } = useI18n() // 国际化
const message = useMessage() // 消息提示
const formRef = ref() // 表单引用

// ================ 画布相关 ================
/**
 * 画布相关元素引用
 */
const canvasParent = ref() // 画布父容器
let map: FabricCanvas // Fabric.js画布实例
let rulerTool: RulerTool // 尺子工具实例
let fenceManager: FenceFactory // 区域围栏管理器

/**
 * 绘制状态管理
 * 负责管理围栏绘制过程中的临时状态和对象引用
 */
const DrawingState = reactive({
  isMouseDown: false,
  drawType: 'none' as 'rectangle' | 'polygon' | 'circle' | 'none',
  fenceType: null as any,
  startPoint: { x: 0, y: 0 } as any,
  fenceDate: {} as FenceDate,
  // 尺子工具相关状态
})

/**
 * 主状态对象
 * 管理整个组件的核心状态
 */
const state = reactive({
  dialogTitle: '', // 对话框标题
  formType: '', // 表单类型：create-新建，update-更新
  floorTree: [] as FloorTree[],
  formData: {} as FenceVO, // 表单数据
  floorData: {} as floorVO, // 楼层数据
  imgInfoSize: {} as Iposition, // 图片尺寸信息
  formLoading: false, // 表单加载状态
  imgLoading: false, // 图片加载状态
  empty: false, // 是否为空
  position: {} as any,
  expiryDate: [] as any, // 日期有效期
  expiryTime: [] as any, // 时间有效期
  areaTree: [] as AreaNode[], // 区域树
  // 移动和缩放相关状态
  move: {
    scaleX: 0,
    scaleY: 0,
    devicePixelRatio: window.devicePixelRatio || 1,
    screenWidth: 0,
    screenHeight: 0
  },
  canvasId: computed(() => 'id-' + Math.random().toString(36).substring(2, 11)), // 画布ID
  // 围栏状态相关
  activeFenceType: 'none' as 'circle' | 'rectangle' | 'polygon' | 'none'
})


// ====================== 计算属性 ======================
const hasRulers = computed(() => {
  if (!map || !map.rulerManager) return false
  rulerConfig.rulerStep
  rulerConfig.isRulerMode
  try {
    const rulers = map.rulerManager.getAllRulers()
    console.log('rulers',rulers);
    
    return rulers && rulers.length > 0
  } catch {
    return false
  }
})
/**
 * 表单验证规则
 */
const formRules = reactive({
  floorId: [{ required: true, message: '楼层id不能为空', trigger: 'change' }],
  fenceName: [{ required: true, message: '围栏名称不能为空', trigger: 'blur' }],
  fenceId: [{ required: true, message: '围栏编号不能为空', trigger: 'blur' }],
  level: [{ required: true, message: '围栏等级不能为空', trigger: 'blur' }]
  // areaIds: [{ required: true, message: '围栏区域不能为空', trigger: 'blur' }],
})


// ================ 数据加载函数 ================
/**
 * 获取区域树数据
 */
const getAreaTree = async () => {
  const data = await AreaApi.getAreaList({ floorId: state.formData.floorId })
  // 将原始数据转换为所需的树结构格式
  state.areaTree = handleTree(data, 'id', 'parentId', 'children')
}

/**
 * 重置地图视图
 */
function resetMapView() {
  if (map?.canvas) {
    map.canvas.setViewportTransform([1, 0, 0, 1, 0, 0])
    map.canvas.renderAll()
  }
}

/**
 * 获取楼层树数据
 */
const getfloorTree = async () => {
  state.floorData = {} as floorVO
  const data = await floorApi.getfloorList()
  state.floorTree = handleTree(data, 'id', 'parentId')
}

/**
 * 处理楼层切换
 * @param value 选中的楼层ID
 */
const handleFloorChange = async (value: number) => {
  if (!value) {
    state.floorData = {} as floorVO
    state.imgInfoSize = undefined as unknown as Iposition
    state.empty = true
    if (map && map.canvas) {
      map.canvas.clear()
    }
    return false
  }

  try {
    state.imgLoading = true
    console.log('开始加载楼层数据, ID:', value)
    state.floorData = await floorApi.getfloor(value)
    console.log('获取到楼层数据:', state.floorData)

    if (state.floorData) {
      if (state.floorData.position) {
        state.imgInfoSize = JSON.parse(state.floorData.position)
        console.log('解析楼层位置信息:', state.imgInfoSize)
      } else {
        console.warn('楼层数据中没有position信息')
      }

      // 楼层变化时获取该楼层的区域
      await getAreaTree()

      if (state.floorData.mapUrl) {
        console.log('开始初始化画布，地图URL:', state.floorData.mapUrl)
        await initCanvas(state.floorData.mapUrl)
      } else {
        console.warn('楼层数据中没有地图URL')
        state.imgLoading = false
      }
    } else {
      console.warn('未找到楼层数据')
      state.imgLoading = false
    }
  } catch (error) {
    console.error('加载楼层数据失败:', error)
    state.imgLoading = false
  }
}

/**
 * 处理区域选择变化
 */
const handleAreaIdsChange = () => {
  state.formData.areaIds.forEach((item: any) => {
    // 在区域树中查找指定ID的区域
    const area = findNode(state.areaTree, (node) => node.id === item)
    if (!area?.position) {
      state.formData.areaIds = state.formData.areaIds.filter((i: any) => i !== item)
      ElMessage.warning('当前区域未绘制，请先绘制区域')
      return
    }
  })
  drawArea()
}

/**
 * 绘制选中的区域
 */
const drawArea = () => {
  map.canvas.getObjects().forEach((item: any) => {
    if (item.id === 'areaFence' || item.id === 'areaFence-dimension') {
      map.canvas.remove(item)
    }
  })
  // 如果没有选择区域，直接返回
  if (!state.formData.areaIds || state.formData.areaIds.length === 0) {
    return
  }


  // 为每个区域设置不同的样式，提高区分度
  state.formData.areaIds.forEach((item: any) => {
    const area = findNode(state.areaTree, (node) => node.id === item) as AreaNode

    if (area?.position) {
      try {

      } catch (e) {
        console.error('解析区域position失败:', e)
      }
    }
  })
}






/**
 * 初始化画布
 * @param url 地图URL
 */
const initCanvas = async (url: string) => {
  try {
    // 清理现有状态
    if (map && map.canvas) {
      try {
        map.canvas.dispose()
        map.canvas = undefined as any
      } catch (e) {
        console.warn('画布销毁失败:', e)
      }
    }

    // 先初始化基本画布
    await drawCanvas()

    if (!map || !map.canvas) {
      console.error('画布未正确初始化')
      return
    }
      // 然后初始化尺子工具
      // 初始化尺子工具的事件监听器
    // 添加缩放事件监听器，处理区域文本的缩放
    map.canvas.on('mouse:wheel', () => {
      // 延迟执行，等待缩放完成
      requestAnimationFrame(() => {
        // 如果有选择的区域，重新绘制
        if (state.formData.areaIds && state.formData.areaIds.length > 0) {
          drawArea();
        }
      });
    });

    // 设置背景图片
    map.setBgImage(url, () => {
      state.imgLoading = false
      state.empty = false
      // 检查比例尺和原点配置
      if (!checkScaleAndOrigin(state.floorData, state.imgInfoSize)) {
        return
      }
      rulerTool = new RulerTool(map, state.imgInfoSize)
      fenceManager = new FenceFactory(map.canvas)
      try {
        // 绘制围栏
          areaDrawing()
        // 无论是否有围栏位置数据，都尝试绘制所选区域
        if (state.formData.areaIds && state.formData.areaIds.length > 0) {
          console.log('尝试绘制所选区域，区域数量:', state.formData.areaIds.length)
          drawArea()
        }
      } catch (error) {
        console.error('处理围栏或区域数据失败:', error)
      }
    })
  } catch (error) {
    console.error('初始化画布失败:', error)
    throw error
  }
}





const areaDrawing = () => {
  // 绘制围栏
  if (state.formData.position) {
    try {
      DrawingState.fenceDate = JSON.parse(state.formData.position)
      if (DrawingState.fenceDate.points && DrawingState.fenceDate.points != null) {
        // 确保先清除已有围栏
        cleanupFenceElements(map)
        addFence(
          DrawingState.fenceDate,
          map,
          state.imgInfoSize,
          getScreenInfo(state.move, canvasParent.value)
        )
        
        // 渲染画布
        map.canvas.renderAll()
      }
    } catch (error) {
      console.error('解析围栏数据失败:', error)
    }
  }
}


const handleDraw = (type: 'circle' | 'rectangle' | 'polygon' | 'clear') => {
  if (!checkScaleAndOrigin(state.floorData, state.imgInfoSize)) {
    return;
  }
  // 清除所有围栏相关元素
  cleanupFenceElements(map);
  // 如果是清除操作，直接返回
  if (type === 'clear') {
    // 重置活动围栏类型
    state.activeFenceType = 'none';
    DrawingState.drawType = 'none';
    // 重置多边形绘制状态
    DrawingState.fenceType = null;
    return;
  }

  // 确保获取最新的缩放比例
  state.move.devicePixelRatio = window.devicePixelRatio || 1;
  if (canvasParent.value) {
    state.move.screenWidth = canvasParent.value.clientWidth;
    state.move.screenHeight = canvasParent.value.clientHeight;
  }
  // 设置当前绘制类型
  DrawingState.drawType = type;
}





/**
 * 创建画布实例
 */
const drawCanvas = async () => {
  try {
    // 确保DOM元素已经渲染
    await nextTick()
    const scaleContainer = canvasParent.value
    if (!scaleContainer) {
      console.error('画布容器不存在')
      return
    }

    // 获取容器尺寸
    const screenWidth = scaleContainer.clientWidth
    const screenHeight = scaleContainer.clientHeight

    // 获取设备像素比（处理高DPI显示器）
    const devicePixelRatio = window.devicePixelRatio || 1;

    // 存储设备像素比到state中，供坐标转换函数使用
    state.move.devicePixelRatio = devicePixelRatio;
    state.move.screenWidth = screenWidth;
    state.move.screenHeight = screenHeight;

    // 创建前确保canvas元素存在
    const canvasElement = document.getElementById(state.canvasId)
    if (!canvasElement) {
      console.error('Canvas元素不存在')
      return
    }

    // 创建FabricCanvas实例，使用原始容器尺寸
    map = new FabricCanvas({
      containerId: state.canvasId,
      width: screenWidth,
      height: screenHeight
    })


    // 加载地图事件监听
    if (map && map.canvas) {
      // 添加缩放限制配置
      map.canvas.on('mouse:wheel', function (opt) {
        const delta = opt.e.deltaY;
        // 限制最小缩放比例，防止区域消失
        if (delta > 0 && map.canvas.getZoom() <= 0.1) {
          return;
        }
        // 确保缩放后正确更新所有对象
        setTimeout(() => {
          map.canvas.getObjects().forEach((obj: any) => {
            if (obj.id === 'fence' || obj.id === 'areaFence') {
              obj.setCoords();
            }
          });
          map.canvas.renderAll();
        }, 10);
      });

      initMapEventListeners()

      // 添加画布拖动功能
      setupCanvasDragging()
    }
  } catch (error) {
    console.error('初始化画布失败:', error)
    throw error
  }
}

/**
 * 初始化地图事件监听
 */
const initMapEventListeners = () => {
  let clientPoint = { x: 0, y: 0 }

  // 鼠标按下事件
  map.canvas.on('mouse:down', (opt) => {
    opt.e.preventDefault()
    const pointer = map.canvas.getScenePoint(opt.e)
    DrawingState.startPoint = pointer
    clientPoint = pointer
    DrawingState.isMouseDown = true
    DrawingState.startPoint = pointer // 设置起始点


    // 使用新的围栏系统进行绘制
    if (DrawingState.drawType !== 'none') {
      map.canvas.isDragging = false

      // 获取围栏工厂实例
      const fenceFactory = fenceManager
      if (!fenceFactory) {
        console.error('围栏工厂实例不存在');
        return;
      }
      switch (DrawingState.drawType) {
        case 'circle':
          // 使用FenceFactory创建圆形围栏
          DrawingState.fenceType = fenceFactory.addCircleFence(pointer, 0)
          break
        case 'rectangle':
          // 使用FenceFactory创建矩形围栏
          DrawingState.fenceType = fenceFactory.addRectangleFence(pointer, 0, 0)
          break
        case 'polygon':
          // 使用FenceFactory绘制多边形
          if (fenceFactory) {
   
            fenceFactory.addPolygonPoint(pointer)
          }
          break
      }
      if (DrawingState.fenceType && DrawingState.drawType !== 'polygon') {
        DrawingState.fenceType.set({
          id: 'fence',
          selectable: false,
          hasBorders: false,
          hasControls: false
        })
        map.canvas.renderAll()
      }
    }
  })

  // 鼠标移动事件
  map.canvas.on('mouse:move', (opt: any) => {
    opt.e.preventDefault()
    const point = map.canvas.getScenePoint(opt.e)
    if (DrawingState.drawType === 'polygon') {
      if (fenceManager && fenceManager.isPolygonDrawing()) {
        fenceManager.updatePolygonRubberBand(point)
        return // 多边形绘制时不执行其他逻辑
      }
    }
    const deltaX = Math.abs(opt.e.clientX - clientPoint.x)
    const deltaY = Math.abs(opt.e.clientY - clientPoint.y)
    const dragDistance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)


    // 简化的拖拽更新逻辑，并添加实时尺寸显示
    if (DrawingState.isMouseDown && DrawingState.startPoint && DrawingState.fenceType && dragDistance > 3) {

      switch (DrawingState.drawType) {
        case 'circle':
          // 计算半径并更新圆形
          const dx = point.x - DrawingState.startPoint.x
          const dy = point.y - DrawingState.startPoint.y
          const radius = Math.sqrt(dx * dx + dy * dy)
          DrawingState.fenceType.set({
            radius: Math.max(radius, 1)
          })

          // 实时显示圆形尺寸
          if (fenceManager) {
            fenceManager.setCircleFence(
              DrawingState.fenceType,
              point,
              DrawingState.startPoint,
              state.imgInfoSize,
              getScreenInfo(state.move, canvasParent.value)
            )
          }
          break
        case 'rectangle':
          // 更新矩形尺寸
          const width = Math.abs(point.x - DrawingState.startPoint.x)
          const height = Math.abs(point.y - DrawingState.startPoint.y)
          const left = point.x < DrawingState.startPoint.x ? point.x : DrawingState.startPoint.x
          const top = point.y < DrawingState.startPoint.y ? point.y : DrawingState.startPoint.y

          DrawingState.fenceType.set({
            left: left,
            top: top,
            width: Math.max(width, 1),
            height: Math.max(height, 1)
          })

          // 实时显示矩形尺寸
          if (fenceManager) {
            fenceManager.setRectangleFence(
              DrawingState.fenceType,
              point,
              DrawingState.startPoint,
              state.imgInfoSize,
              getScreenInfo(state.move, canvasParent.value)
            )
          }
          break
      }

      map.canvas.renderAll()
    }
  })

  // 鼠标释放事件
  map.canvas.on('mouse:up', (opt: any) => {
    opt.e.preventDefault()

    // 如果正在拖动围栏，结束拖动
    if (dragState.isDragging) {
      dragState.isDragging = false;

      // 恢复鼠标样式
      document.body.style.cursor = 'default';



      return; // 不执行后续的绘制逻辑
    }

    if (
      DrawingState.isMouseDown &&
      (DrawingState.drawType == 'rectangle' || DrawingState.drawType == 'circle')
    ) {
      drawDone()
    }
    // 多边形绘制在鼠标释放时不需要额外处理，因为点的添加已经在鼠标按下时完成
    DrawingState.isMouseDown = false
  })

  // 鼠标双击事件
  map.canvas.on('mouse:dblclick', (opt: any) => {
    opt.e.preventDefault()
    if (DrawingState.drawType == 'polygon') {
      console.log('双击完成多边形绘制');

      // 使用FenceFactory完成多边形绘制
 
      if (fenceManager && fenceManager.isPolygonDrawing()) {
        const currentPoint = map.canvas.getScenePoint(opt.e)
        const polygon = fenceManager.finishPolygonDrawing(currentPoint)

        if (!polygon) {
          ElMessage.warning('多边形至少需要3个点')
          return
        }

        // 设置DrawingState
        DrawingState.fenceType = polygon

        console.log('多边形绘制完成')

        // 完成绘制
        drawDone()
      }
    }
  })

  // 添加缩放事件监听
  map.canvas.on('mouse:wheel', (opt) => {
    opt.e.preventDefault()
    // 添加新的缩放处理逻辑
    const delta = opt.e.deltaY
    // 限制最小缩放比例，防止区域消失
    if (delta > 0 && map.canvas.getZoom() <= 0.1) {
      return
    }

    // 确保在缩放后调用调整对象大小和位置的方法
    requestAnimationFrame(() => {
      // 调用调整对象大小和位置的方法
      map.adjustObjectSizes()

      // 缩放处理现在由EditableFence自动处理
    })
  })

  // 添加缩放完成后的事件处理（使用Fabric.js支持的事件）
  map.canvas.on('after:render', () => {
    // 确保在缩放后所有围栏仍然可见
    map.canvas.getObjects().forEach((obj: any) => {
      if (obj.id === 'fence' || obj.id === 'areaFence') {
        obj.setCoords() // 更新对象坐标
      }
    })
  })
}



/**
 * 切换尺子模式
 */
const toggleRulerMode = () => {
  if (!rulerTool) return

  rulerTool.toggleRulerMode()


}

// 定义全局拖动状态变量
const dragState = reactive({
  isDragging: false,
  lastX: 0,
  lastY: 0,
  offsetX: 0,
  offsetY: 0
});

const drawDone = () => {
  // 保存当前绘制类型
  const currentType = DrawingState.drawType
  ElMessage.success(
    `绘制${currentType == 'polygon' ? '多边形' : currentType == 'rectangle' ? '矩形' : currentType == 'circle' ? '圆形' : ''}完成`
  )

  // 保存当前围栏类型到state中，用于尺寸显示
  state.activeFenceType = currentType;

  // 保存原始围栏的数据，然后移除原始围栏
  let fenceData: any = null;
  if (DrawingState.fenceType && currentType !== 'none') {
    // 根据围栏类型获取数据
    switch (currentType) {
      case 'circle':
        if (DrawingState.fenceType.radius > 0) {
          fenceData = {
            center: { x: DrawingState.fenceType.left, y: DrawingState.fenceType.top },
            radius: DrawingState.fenceType.radius
          };
        }
        break;
      case 'rectangle':
        if (DrawingState.fenceType.width > 0 && DrawingState.fenceType.height > 0) {
          fenceData = {
            position: { x: DrawingState.fenceType.left, y: DrawingState.fenceType.top },
            width: DrawingState.fenceType.width,
            height: DrawingState.fenceType.height
          };
        }
        break;
      case 'polygon':
        // 多边形数据从Fabric.js多边形对象获取，需要转换为实际坐标（米单位）
        if (DrawingState.fenceType && DrawingState.fenceType.points && DrawingState.fenceType.points.length >= 3) {

        }
        break;
    }


  }


  // 完成绘制
  map.canvas.renderAll()

  // 退出绘制状态
  DrawingState.drawType = 'none';
  DrawingState.isMouseDown = false;
  DrawingState.startPoint = null;
}

// 拖拽相关函数已移除，现在由EditableFence自动处理



// ================ 表单操作函数 ================
/**
 * 返回上一页
 */
const goBack = () => {
  ElMessageBox.confirm('确定要离开当前页面吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      router.back()
    })
    .catch(() => { })
}

const handleExpiryDateChange = (value: any) => {
  state.formData.expiryDate = `${value[0]}~${value[1]}`
}

const handleExpiryTimeChange = (value: any) => {
  state.formData.expiryTime = `${value[0]}~${value[1]}`
}



/**
 * 提交表单
 */
const submitForm = async () => {
  try {
    // 校验表单
    await formRef.value.validate()
      state.formData.position = JSON.stringify(DrawingState.fenceDate)
    console.log('提交的areaIds:', state.formData.areaIds)
    
    if (state.formType === 'create') {
      await FenceApi.createFence(state.formData)
      message.success(t('common.createSuccess'))
    } else {
      await FenceApi.updateFence(state.formData)
      message.success(t('common.updateSuccess'))
    }

    // 返回上一页并带上刷新标志
    router.push({
      name: 'Fence',
      query: { refresh: Date.now().toString() }
    })
  } catch (error) {
    console.error('提交表单出错:', error)
  }
}

/**
 * 获取基站详情
 */
const getDetail = async () => {
  const id = route.query.id as unknown as number
  if (!id) {
    state.empty = true
    state.dialogTitle = '添加围栏'
    state.formType = 'create'
    return
  }
  state.formLoading = true
  try {
    state.dialogTitle = '修改围栏'
    state.formType = 'update'

    // 获取围栏详情
    const result = await FenceApi.getFence(id)
    console.log('获取到围栏详情:', result);

    // 确保areaIds中的每个id都是数值类型
    result.areaIds = Array.isArray(result.areaIds)
      ? result.areaIds.map((id: string | number) => (typeof id === 'string' ? Number(id) : id))
      : []

    console.log('处理后的areaIds:', result.areaIds);

    state.formData = result

    if (state.formData.expiryTime) {
      state.expiryTime = [
        state.formData.expiryTime.split('~')[0],
        state.formData.expiryTime.split('~')[1]
      ]
    }
    if (state.formData.expiryDate) {
      state.expiryDate = [
        state.formData.expiryDate.split('~')[0],
        state.formData.expiryDate.split('~')[1]
      ]
    }

    // 先加载楼层数据，这会触发区域数据加载和画布初始化
    if (state.formData.floorId) {
      console.log('加载楼层数据，ID:', state.formData.floorId);
      await handleFloorChange(state.formData.floorId)
    } else {
      console.warn('围栏数据中没有floorId');
    }
  } catch (error) {
    console.error('获取围栏详情失败:', error);
  } finally {
    state.formLoading = false
  }
}

onMounted(async () => {
  try {
    await getfloorTree()
    await getDetail() // 先获取详情，因为它会设置floorId
    
    // 添加键盘事件监听器（在尺子工具初始化后添加）
    if (rulerTool) {
      document.addEventListener('keydown', rulerTool.handleKeyDown);
    }
  } catch (error) {
    console.error('初始化数据失败:', error)
  }
})


/**
 * 添加画布拖动功能
 * 直接在canvas元素上添加事件监听器，而不是使用Fabric.js的事件系统
 */
const setupCanvasDragging = () => {
  const canvasElement = document.getElementById(state.canvasId);
  if (!canvasElement) return;

  let isDragging = false;
  let lastX = 0;
  let lastY = 0;
  let isFenceDragging = false; // 标记是否正在拖动围栏

  // 添加画布事件监听器，检测是否正在拖动围栏
  map.canvas.on('object:moving', () => {
    isFenceDragging = true;
  });
  
  map.canvas.on('mouse:up', () => {
    // 鼠标释放后重置围栏拖动状态
    setTimeout(() => {
      isFenceDragging = false;
    }, 50); // 短暂延迟，确保其他事件处理完成
  });

  // 鼠标按下事件
  canvasElement.addEventListener('mousedown', (e) => {
    // 如果正在拖动围栏，则不启用画布拖动
    if (isFenceDragging || dragState.isDragging || DrawingState.drawType !== 'none') return;

    // 检查是否点击了围栏对象
    const activeObject = map.canvas.getActiveObject();
    if (activeObject && (activeObject.id === 'fence' || activeObject.type === 'circle' || activeObject.type === 'rect' || activeObject.type === 'polygon')) {
      isFenceDragging = true;
      return;
    }

    isDragging = true;
    lastX = e.clientX;
    lastY = e.clientY;
    canvasElement.style.cursor = 'grabbing';
  });

  // 鼠标移动事件
  window.addEventListener('mousemove', (e) => {
    // 如果正在拖动围栏，不进行画布拖动
    if (isFenceDragging) return;
    
    if (!isDragging) return;

    const deltaX = e.clientX - lastX;
    const deltaY = e.clientY - lastY;
    lastX = e.clientX;
    lastY = e.clientY;

    // 移动画布
    const vpt = map.canvas.viewportTransform;
    if (vpt) {
      vpt[4] += deltaX;
      vpt[5] += deltaY;
      map.canvas.requestRenderAll();
    }
  });

  // 鼠标松开事件
  window.addEventListener('mouseup', () => {
    if (isDragging) {
      isDragging = false;
      canvasElement.style.cursor = 'default';
    }
    
    // 短暂延迟后重置围栏拖动状态
    setTimeout(() => {
      isFenceDragging = false;
    }, 100);
  });

  // 鼠标离开画布事件
  canvasElement.addEventListener('mouseleave', () => {
    if (isDragging) {
      isDragging = false;
      canvasElement.style.cursor = 'default';
    }

    // 如果正在拖动围栏，也结束拖动
    if (dragState.isDragging) {
      dragState.isDragging = false;
      document.body.style.cursor = 'default';
    }
    
    // 重置围栏拖动状态
    isFenceDragging = false;
  });
}



onUnmounted(() => {
  // 移除键盘事件监听器 - 如果rulerTool存在，它会在自己的清理方法中处理
  if (rulerTool) {
    document.removeEventListener('keydown', rulerTool.handleKeyDown);
  }

  // 清理尺子
  if (rulerTool) {
    // 清理尺子和事件监听器
    rulerTool.removeAllRulers();
    // 在RulerTool类中添加清理方法来移除事件监听器
    if (typeof rulerTool.cleanup === 'function') {
      rulerTool.cleanup();
    } else {
      console.error('rulerTool.cleanup 方法不存在');
    }
  }
});









</script>

<style scoped>
body {
  margin: 0;
}

.example-showcase .el-loading-mask {
  z-index: 9;
}

:deep(.el-card__header) {
  padding: 10px 20px;
  background-color: #f9f9f9;
  border-bottom: 1px solid #f0f0f0;
}


.content-container {
  width: 100%;
  height: calc(100vh - 330px);
  background-color: #f5f7fa;
}

.el-form-item {
  margin-bottom: 12px !important;
}

/* 禁用输入框的特殊样式 */
:deep(.el-input.is-disabled .el-input__inner) {
  color: #606266;
  background-color: #f8f9fa;
  border-color: #e4e7ed;
}

/* 提示框样式 */
.instruction-alert {
  margin: 15px 0;
  border-radius: 4px;
}

:deep(.el-alert__title) {
  font-weight: bold;
}

:deep(.el-alert__description) {
  margin: 8px 0 0;
  line-height: 1.6;
}

/* 尺寸信息显示 */
.dimension-info {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 1000;
  padding: 8px 12px;
  font-size: 14px;
  color: white;
  pointer-events: none;
  background-color: rgb(0 0 0 / 70%);
  border-radius: 4px;
}


.status-indicator {
  position: absolute;
  top: 8vh;
  left: 2vw;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.operation-tips {
  max-width: 400px;
  padding: 12px 16px;
  font-size: 12px;
  line-height: 1.6;
  color: #606266;
  background-color: rgb(255 255 255 / 95%);
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgb(0 0 0 / 15%);
  backdrop-filter: blur(8px);
}

.tip-line {
  margin-bottom: 6px;
  font-weight: 500;
  color: #409eff;
  word-wrap: break-word;
}

.tip-line:last-child {
  margin-bottom: 0;
}

.delete-tip {
  padding: 4px 8px;
  margin-top: 4px;
  font-weight: 600;
  color: #f56c6c !important;
  background-color: rgb(245 108 108 / 10%);
  border: 1px solid rgb(245 108 108 / 30%);
  border-radius: 4px;
}
</style>
