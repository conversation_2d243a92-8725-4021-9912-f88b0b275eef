import { canvasToImageWithDPR } from "@/store/modules/canvas/drawing";
import { Circle, IText, Line, Polygon, Rect, Shadow } from "fabric";

/**
 * 围栏工厂类
 * 封装所有围栏创建和操作方法，从FabricCanvas中迁移而来
 */
export class FenceFactory {
  private canvas: any;
  private fenceColor: { color: string; stroke: string; strokeWidth: number };
  private polygonDrawing: boolean = false;
  private realtimeDimensionTexts: any[] = [];
  private polygonPoints: { x: number; y: number }[] = []; // 保存多边形点
  private polygonPreviewLine: any = null; // 多边形预览线
  private polygonPreviewShape: any = null; // 多边形预览形状

  constructor(canvas: any) {
    this.canvas = canvas;
    this.fenceColor = {
      color: 'rgba(30, 144, 255, 0.3)',
      stroke: '#1e90ff',
      strokeWidth: 2
    };
  }

  /**
   * 获取围栏颜色配置
   */
  getFenceColor() {
    return this.fenceColor;
  }

  /**
   * 添加圆形围栏
   */
  addCircleFence(center: { x: number; y: number }, radius: number) {
    const circle = new Circle({
      left: center.x,
      top: center.y,
      radius: radius,
      fill: this.fenceColor.color,
      stroke: this.fenceColor.stroke,
      strokeWidth: this.fenceColor.strokeWidth,
      originX: 'center',
      originY: 'center',
      lockUniScaling: true, // 锁定等比例缩放，确保圆形保持圆形
      strokeUniform: true, // 保持边框宽度一致，不随缩放变化
      noScaleCache: false,
      objectCaching: false
    });
    
    this.canvas.add(circle);
    return circle;
  }

  /**
   * 添加矩形围栏
   */
  addRectangleFence(position: { x: number; y: number }, width: number, height: number) {
    const rectangle = new Rect({
      left: position.x,
      top: position.y,
      width: width,
      height: height,
      fill: this.fenceColor.color,
      stroke: this.fenceColor.stroke,
      strokeWidth: this.fenceColor.strokeWidth,
      strokeUniform: true, // 保持边框宽度一致，不随缩放变化
      noScaleCache: false,
      objectCaching: false
    });
    
    this.canvas.add(rectangle);
    return rectangle;
  }

  /**
   * 创建多边形围栏
   */
  createPolygonFence(points: { x: number; y: number }[]) {
    if (!points || points.length < 3) return null;
    
    const polygon = new Polygon(points, {
      fill: this.fenceColor.color,
      stroke: this.fenceColor.stroke,
      strokeWidth: this.fenceColor.strokeWidth
    });
    
    this.canvas.add(polygon);
    return polygon;
  }
  
  /**
   * 添加多边形点
   * @param point 新的多边形点坐标
   */
  addPolygonPoint(point: { x: number; y: number }) {
    if (!this.polygonDrawing) {
      this.startPolygonDrawing();
    }
    
    this.polygonPoints.push(point);
    this.updatePolygonPreview();
    return true;
  }
  
  /**
   * 完成多边形绘制
   * @param lastPoint 最后一个点（可选）
   * @returns 创建的多边形对象，如果点数不足则返回null
   */
  finishPolygonDrawing(lastPoint?: { x: number; y: number }): any {
    // 如果提供了最后一个点，检查是否与第一个点接近
    if (lastPoint && this.polygonPoints.length > 0) {
      const firstPoint = this.polygonPoints[0];
      const distToFirst = Math.sqrt(
        Math.pow(lastPoint.x - firstPoint.x, 2) + 
        Math.pow(lastPoint.y - firstPoint.y, 2)
      );
      
      // 如果距离较远，添加最后一个点；否则自动闭合
      if (distToFirst > 10) {
        this.polygonPoints.push(lastPoint);
      }
    }
    
    // 确保至少有3个点
    if (this.polygonPoints.length < 3) {
      console.warn('多边形至少需要3个点');
      this.clearPolygonPreview();
      this.polygonDrawing = false;
      this.polygonPoints = [];
      return null;
    }
    
    // 创建最终的多边形
    const currentZoom = this.canvas.getZoom();
    const strokeWidth = 2 / currentZoom;
    
    const polygon = new Polygon(this.polygonPoints, {
      fill: this.fenceColor.color,
      stroke: this.fenceColor.stroke,
      strokeWidth: strokeWidth,
        selectable: false,
        evented: false,
        objectCaching: false
    });
    (polygon as any).id = 'fence';
    
    this.canvas.add(polygon);
    
    // 清理预览
    this.clearPolygonPreview();
    
    // 重置状态
    const resultPoints = [...this.polygonPoints];
    this.polygonDrawing = false;
    this.polygonPoints = [];
    
    console.log('多边形绘制完成，点数:', resultPoints.length);
    this.canvas.renderAll();
    
    return polygon;
  }

  /**
   * 设置圆形围栏
   */
  setCircleFence(circle: any, point: { x: number; y: number }, startPoint: { x: number; y: number }, imgInfoSize?: any, screenInfo?: any) {
    if (!circle) return;
    
    // 计算半径
    const dx = point.x - startPoint.x;
    const dy = point.y - startPoint.y;
    const radius = Math.sqrt(dx * dx + dy * dy);
    
    // 确保在高缩放级别下边框仍然可见
    const currentZoom = this.canvas.getZoom();
    const minStrokeWidth = 1.5 / currentZoom;
    const strokeWidth = Math.max(this.fenceColor.strokeWidth, minStrokeWidth);
    
    // 更新圆形属性
    circle.set({
      radius: Math.max(radius, 1),
      lockUniScaling: true, // 锁定等比例缩放，确保圆形保持圆形
      strokeWidth: strokeWidth,
      strokeUniform: true // 保持边框宽度一致，不随缩放变化
    });
    
    // 更新实时尺寸显示
    this.updateRealtimeCircleDimension(circle, startPoint, imgInfoSize, screenInfo);
    
    this.canvas.renderAll();
  }

  /**
   * 设置矩形围栏
   */
  setRectangleFence(rectangle: any, point: { x: number; y: number }, startPoint: { x: number; y: number }, imgInfoSize?: any, screenInfo?: any) {
    if (!rectangle) return;
    
    // 计算宽高
    const width = Math.abs(point.x - startPoint.x);
    const height = Math.abs(point.y - startPoint.y);
    
    // 确定矩形左上角位置
    const left = Math.min(point.x, startPoint.x);
    const top = Math.min(point.y, startPoint.y);
    
    // 确保在高缩放级别下边框仍然可见
    const currentZoom = this.canvas.getZoom();
    const minStrokeWidth = 1.5 / currentZoom;
    const strokeWidth = Math.max(this.fenceColor.strokeWidth, minStrokeWidth);
    
    // 更新矩形属性
    rectangle.set({
      left: left,
      top: top,
      width: Math.max(width, 1),
      height: Math.max(height, 1),
      strokeWidth: strokeWidth,
      strokeUniform: true // 保持边框宽度一致，不随缩放变化
    });
    
    // 更新实时尺寸显示
    this.updateRealtimeRectangleDimension(rectangle, imgInfoSize, screenInfo);
    
    this.canvas.renderAll();
  }

  /**
   * 更新半径线
   */
  updateRadiusLine(radiusLine: any, center: { x: number; y: number }, endPoint: { x: number; y: number }) {
    if (!radiusLine) return;
    
    radiusLine.set({
      x1: center.x,
      y1: center.y,
      x2: endPoint.x,
      y2: endPoint.y
    });
    
    this.canvas.renderAll();
  }

  /**
   * 更新实时圆形尺寸标注
   */
  updateRealtimeCircleDimension(circle: any, center: { x: number; y: number }, imgInfoSize?: any, screenInfo?: any) {
    // 清除之前的实时标注
    this.clearRealtimeDimensionTexts();
    
    const currentZoom = this.canvas.getZoom();
    const zoomFactor = 1 / currentZoom;
    let radiusText;
    
    // 获取实际半径（考虑缩放因素）
    const actualRadius = circle.getScaledWidth ? circle.getScaledWidth() / 2 : circle.radius;
    
    // 计算适当的显示距离，确保文本不会太近或太远
    const displayDistance = Math.max(15 * zoomFactor, 10 / currentZoom);
    
    // 计算缩放后的合适字体大小
    const minFontSize = 12; // 最小字体大小
    const maxFontSize = 30; // 最大字体大小
    let fontSize = 15 * zoomFactor;
    
    // 限制字体大小在合理范围内
    if (fontSize < minFontSize / currentZoom) {
      fontSize = minFontSize / currentZoom;
    } else if (fontSize > maxFontSize / currentZoom) {
      fontSize = maxFontSize / currentZoom;
    }
    
    // 创建新的半径标注
    if (imgInfoSize && screenInfo) {
      // 转换为实际距离
    const realRadius = canvasToImageWithDPR(
        { x: center.x, y: center.y, radius: actualRadius },
        { canvas: this.canvas },
      imgInfoSize,
      screenInfo
      ).radius;
    
      radiusText = new IText(`${realRadius.toFixed(2)}`, {
        left: center.x + actualRadius + displayDistance,
      top: center.y,
        fontSize: fontSize,
        fontFamily: 'Microsoft YaHei, Arial, sans-serif',
        fontWeight: 'bold',
        fill: '#f50', // 使用尺子的文本颜色
        stroke: '#ffffff',
        strokeWidth: 0.5 / currentZoom, // 描边宽度随缩放调整
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        textBackgroundColor: 'rgba(255, 255, 255, 0.95)',
        padding: 4 / currentZoom, // 内边距随缩放调整
      originX: 'left',
      originY: 'center',
        shadow: new Shadow({
          color: 'rgba(0,0,0,0.3)',
          blur: 4,
          offsetX: 1,
          offsetY: 2
        })
      });
    } else {
      // 直接使用画布距离
      radiusText = new IText(`${actualRadius.toFixed(2)}`, {
        left: center.x + actualRadius + displayDistance,
        top: center.y,
        fontSize: fontSize,
        fontFamily: 'Microsoft YaHei, Arial, sans-serif',
        fontWeight: 'bold',
        fill: '#f50', // 使用尺子的文本颜色
        stroke: '#ffffff',
        strokeWidth: 0.5 / currentZoom, // 描边宽度随缩放调整
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        textBackgroundColor: 'rgba(255, 255, 255, 0.95)',
        padding: 4 / currentZoom, // 内边距随缩放调整
        originX: 'left',
        originY: 'center',
        shadow: new Shadow({
          color: 'rgba(0,0,0,0.3)',
          blur: 4,
          offsetX: 1,
          offsetY: 2
        })
      });
    }
    
    this.realtimeDimensionTexts.push(radiusText);
    this.canvas.add(radiusText);
    
    // 确保文本在顶层
    if (radiusText) {
      this.canvas.remove(radiusText);
      this.canvas.add(radiusText);
    }
    
    this.canvas.renderAll();
  }

  /**
   * 更新实时矩形尺寸标注
   */
  updateRealtimeRectangleDimension(rectangle: any, imgInfoSize?: any, screenInfo?: any) {
    // 清除之前的实时标注
    this.clearRealtimeDimensionTexts();
    
    const currentZoom = this.canvas.getZoom();
    const zoomFactor = 1 / currentZoom;
    
    // 获取矩形实际尺寸（考虑缩放）
    const actualWidth = rectangle.getScaledWidth ? rectangle.getScaledWidth() : rectangle.width;
    const actualHeight = rectangle.getScaledHeight ? rectangle.getScaledHeight() : rectangle.height;
    
    // 计算适当的显示距离，确保文本不会太近或太远
    const displayDistance = Math.max(15 * zoomFactor, 10 / currentZoom);
    
    // 计算缩放后的合适字体大小
    const minFontSize = 12; // 最小字体大小
    const maxFontSize = 30; // 最大字体大小
    let fontSize = 15 * zoomFactor;
    
    // 限制字体大小在合理范围内
    if (fontSize < minFontSize / currentZoom) {
      fontSize = minFontSize / currentZoom;
    } else if (fontSize > maxFontSize / currentZoom) {
      fontSize = maxFontSize / currentZoom;
    }
    
    let widthText, heightText;
    
    // 创建新的宽度和高度标注
    if (imgInfoSize && screenInfo) {
      // 转换为实际距离
    const realDimensions = canvasToImageWithDPR(
        { 
          x: rectangle.left, 
          y: rectangle.top, 
          width: actualWidth, 
          height: actualHeight 
        },
        { canvas: this.canvas },
      imgInfoSize,
      screenInfo
      );
      
      // 宽度标注
      widthText = new IText(`${realDimensions.width.toFixed(2)}`, {
        left: rectangle.left + actualWidth / 2,
        top: rectangle.top - displayDistance,
        fontSize: fontSize,
        fontFamily: 'Microsoft YaHei, Arial, sans-serif',
        fontWeight: 'bold',
        fill: '#1890ff', // 使用尺子的蓝色
        stroke: '#ffffff',
        strokeWidth: 0.5 / currentZoom, // 描边宽度随缩放调整
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        textBackgroundColor: 'rgba(255, 255, 255, 0.95)',
        padding: 4 / currentZoom, // 内边距随缩放调整
      originX: 'center',
      originY: 'bottom',
        shadow: new Shadow({
          color: 'rgba(0,0,0,0.3)',
          blur: 4,
          offsetX: 1,
          offsetY: 2
        })
      });
      
      // 高度标注
      heightText = new IText(`${realDimensions.height.toFixed(2)}`, {
        left: rectangle.left - displayDistance,
        top: rectangle.top + actualHeight / 2,
        fontSize: fontSize,
        fontFamily: 'Microsoft YaHei, Arial, sans-serif',
        fontWeight: 'bold',
        fill: '#1890ff', // 使用尺子的蓝色
        stroke: '#ffffff',
        strokeWidth: 0.5 / currentZoom, // 描边宽度随缩放调整
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        textBackgroundColor: 'rgba(255, 255, 255, 0.95)',
        padding: 4 / currentZoom, // 内边距随缩放调整
      originX: 'right',
      originY: 'center',
        shadow: new Shadow({
          color: 'rgba(0,0,0,0.3)',
          blur: 4,
          offsetX: 1,
          offsetY: 2
        })
      });
    } else {
      // 直接使用画布距离
      widthText = new IText(`${actualWidth.toFixed(2)}`, {
        left: rectangle.left + actualWidth / 2,
        top: rectangle.top - displayDistance,
        fontSize: fontSize,
        fontFamily: 'Microsoft YaHei, Arial, sans-serif',
        fontWeight: 'bold',
        fill: '#1890ff', // 使用尺子的蓝色
        stroke: '#ffffff',
        strokeWidth: 0.5 / currentZoom, // 描边宽度随缩放调整
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        textBackgroundColor: 'rgba(255, 255, 255, 0.95)',
        padding: 4 / currentZoom, // 内边距随缩放调整
        originX: 'center',
        originY: 'bottom',
        shadow: new Shadow({
          color: 'rgba(0,0,0,0.3)',
          blur: 4,
          offsetX: 1,
          offsetY: 2
        })
      });
      
      heightText = new IText(`${actualHeight.toFixed(2)}`, {
        left: rectangle.left - displayDistance,
        top: rectangle.top + actualHeight / 2,
        fontSize: fontSize,
        fontFamily: 'Microsoft YaHei, Arial, sans-serif',
        fontWeight: 'bold',
        fill: '#1890ff', // 使用尺子的蓝色
        stroke: '#ffffff',
        strokeWidth: 0.5 / currentZoom, // 描边宽度随缩放调整
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        textBackgroundColor: 'rgba(255, 255, 255, 0.95)',
        padding: 4 / currentZoom, // 内边距随缩放调整
        originX: 'right',
        originY: 'center',
        shadow: new Shadow({
          color: 'rgba(0,0,0,0.3)',
          blur: 4,
          offsetX: 1,
          offsetY: 2
        })
      });
    }
    
    this.realtimeDimensionTexts.push(widthText, heightText);
    
    // 确保文本在顶层 - 先添加，再删除重新添加
    this.canvas.add(widthText);
    this.canvas.add(heightText);
    
    // 确保文本在顶层
    if (widthText) {
      this.canvas.remove(widthText);
      this.canvas.add(widthText);
    }
    
    if (heightText) {
      this.canvas.remove(heightText);
      this.canvas.add(heightText);
    }
    
    this.canvas.renderAll();
  }

  /**
   * 清理实时尺寸文本
   */
  clearRealtimeDimensionTexts() {
    this.realtimeDimensionTexts.forEach(text => {
      if (text) {
        this.canvas.remove(text);
      }
    });
    this.realtimeDimensionTexts = [];
  }

  /**
   * 开始多边形绘制
   */
  startPolygonDrawing() {
    this.polygonDrawing = true;
    this.polygonPoints = [];
    this.clearPolygonPreview();
    console.log('开始多边形绘制');
  }

  /**
   * 是否正在绘制多边形
   */
  isPolygonDrawing() {
    return this.polygonDrawing;
  }

  /**
   * 取消多边形绘制
   */
  cancelPolygonDrawing() {
    this.polygonDrawing = false;
    this.polygonPoints = [];
    this.clearPolygonPreview();
    console.log('取消多边形绘制');
  }

  /**
   * 更新多边形橡皮筋效果
   * @param mousePoint 当前鼠标位置
   */
  updatePolygonRubberBand(mousePoint: { x: number; y: number }) {
    if (!this.polygonDrawing || this.polygonPoints.length === 0) return;
    
    // 清除之前的预览
    this.clearPolygonPreview();
    
    const currentZoom = this.canvas.getZoom();
    const strokeWidth = 2 / currentZoom;
    
    // 如果只有一个点，绘制从第一个点到鼠标的线
    if (this.polygonPoints.length === 1) {
      this.polygonPreviewLine = new Line(
        [this.polygonPoints[0].x, this.polygonPoints[0].y, mousePoint.x, mousePoint.y],
        {
          stroke: this.fenceColor.stroke,
          strokeWidth: strokeWidth,
          selectable: false,
          evented: false,
          objectCaching: false
        }
      );
      (this.polygonPreviewLine as any).id = 'polygon-preview-line';
      this.canvas.add(this.polygonPreviewLine);
    }
    // 如果有两个或更多点，绘制完整的预览多边形
    else if (this.polygonPoints.length >= 2) {
      // 创建包含鼠标位置的临时点数组
      const previewPoints = [...this.polygonPoints, mousePoint];
      
      // 创建预览多边形（使用更透明的填充）
      const previewFill = this.fenceColor.color.replace(/[\d\.]+\)$/g, '0.1)'); // 将透明度改为0.1
      
      this.polygonPreviewShape = new Polygon(previewPoints, {
        fill: previewFill,
        stroke: this.fenceColor.stroke,
        strokeWidth: strokeWidth,
      selectable: false,
        evented: false,
        objectCaching: false
      });
      (this.polygonPreviewShape as any).id = 'polygon-preview';
      
      this.canvas.add(this.polygonPreviewShape);
    }
    
    this.canvas.renderAll();
  }
  
  /**
   * 更新多边形预览
   */
  private updatePolygonPreview() {
    // 清除之前的预览
    this.clearPolygonPreview();
    
    // 如果有足够的点，创建临时多边形
    if (this.polygonPoints.length >= 3) {
      const currentZoom = this.canvas.getZoom();
      const strokeWidth = 2 / currentZoom;
      
      this.polygonPreviewShape = new Polygon(this.polygonPoints, {
        fill: this.fenceColor.color,
        stroke: this.fenceColor.stroke,
        strokeWidth: strokeWidth,
      selectable: false,
        evented: false,
        objectCaching: false
      });
      (this.polygonPreviewShape as any).id = 'polygon-preview';
      
      this.canvas.add(this.polygonPreviewShape);
    }
    
    this.canvas.renderAll();
  }
  
  /**
   * 清除多边形预览
   */
  private clearPolygonPreview() {
    // 移除预览线
    if (this.polygonPreviewLine) {
      this.canvas.remove(this.polygonPreviewLine);
      this.polygonPreviewLine = null;
    }
    
    // 移除预览形状
    if (this.polygonPreviewShape) {
      this.canvas.remove(this.polygonPreviewShape);
      this.polygonPreviewShape = null;
    }
    
    // 清除所有预览相关的对象
    const objectsToRemove = this.canvas.getObjects().filter((obj: any) => 
      obj.id === 'polygon-preview' || 
      obj.id === 'polygon-preview-line'
    );
    
    objectsToRemove.forEach((obj: any) => {
      this.canvas.remove(obj);
    });
  }
}


